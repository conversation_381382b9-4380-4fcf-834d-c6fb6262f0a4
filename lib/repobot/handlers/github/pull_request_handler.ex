defmodule Repobot.Handlers.GitHub.PullRequestHandler do
  @moduledoc """
  Handles GitHub pull request events by updating pull request statuses and logging events.
  """

  require Logger

  alias Repobot.{Events, Repositories, PullRequests, RepositoryFiles}

  defmodule PullRequest do
    @moduledoc """
    Represents a GitHub pull request event with processed attributes for easier handling.
    """
    defstruct [
      :repository_full_name,
      :repository_id,
      :organization_id,
      :number,
      :title,
      :html_url,
      :action,
      :merged,
      :pull_request
    ]

    @type t :: %__MODULE__{
            repository_full_name: String.t(),
            repository_id: integer() | nil,
            organization_id: integer() | nil,
            number: integer(),
            title: String.t(),
            html_url: String.t(),
            action: String.t(),
            merged: boolean(),
            pull_request: Repobot.PullRequests.PullRequest.t() | nil
          }

    @doc """
    Creates a new PullRequest struct from a GitHub webhook payload.
    """
    def new(payload) do
      repository_full_name = payload["repository"]["full_name"]
      repository = Repositories.get_repository_by(full_name: repository_full_name)

      %__MODULE__{
        repository_full_name: repository_full_name,
        repository_id: repository && repository.id,
        organization_id: repository && repository.organization_id,
        number: payload["pull_request"]["number"],
        title: payload["pull_request"]["title"],
        html_url: payload["pull_request"]["html_url"],
        action: payload["action"],
        merged: payload["pull_request"]["merged"],
        pull_request: nil
      }
    end

    @doc """
    Loads the associated pull request from the database.
    Returns {:ok, pull_request} if found, {:ok, nil} if not found.
    """
    def load_pull_request(%__MODULE__{repository_full_name: repo, number: number} = pr) do
      case PullRequests.get_pull_request_by(
             repository: repo,
             pull_request_number: number
           ) do
        nil ->
          Logger.info("Pull request not found: #{repo}##{number}")
          {:ok, %{pr | pull_request: nil}}

        pull_request ->
          {:ok, %{pr | pull_request: pull_request}}
      end
    end

    @doc """
    Determines the status of a pull request based on its action and merged state.
    Returns the status string or nil if no status change is needed.
    """
    def get_status(%__MODULE__{action: action, merged: merged}) do
      case {action, merged} do
        {"closed", true} -> "merged"
        {"closed", false} -> "closed"
        {"reopened", _} -> "open"
        _ -> nil
      end
    end
  end

  @doc """
  Handles a GitHub pull request event.
  """
  def handle(payload) do
    pr = PullRequest.new(payload)

    # Log the event
    if pr.organization_id do
      Events.log_github_event(
        "pull_request",
        payload,
        pr.organization_id,
        nil,
        pr.repository_id
      )
    end

    Logger.info("""
    Received pull_request event:
      Repository: #{pr.repository_full_name}
      PR number: #{pr.number}
      Action: #{pr.action}
      Merged: #{pr.merged}
      Title: #{pr.title}
      URL: #{pr.html_url}
    """)

    # Check if this is a merged PR to the default branch and trigger repository refresh
    if pr.action == "closed" && pr.merged && pr.repository_id do
      refresh_repository_files_for_merged_pr(pr)
    end

    with {:ok, pr} <- PullRequest.load_pull_request(pr),
         status when not is_nil(status) <- PullRequest.get_status(pr),
         :ok <- update_pull_request_status(pr, status) do
      :ok
    else
      nil ->
        # No status change needed
        :ok

      {:error, reason} ->
        Logger.error("Error processing pull request: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp update_pull_request_status(%PullRequest{pull_request: nil}, _status), do: :ok

  defp update_pull_request_status(
         %PullRequest{repository_full_name: repo, number: number, pull_request: pull_request},
         status
       ) do
    Logger.info("Updating PR #{repo}##{number} status to: #{status}")

    case PullRequests.update_pull_request(pull_request, %{status: status}) do
      {:ok, _updated} ->
        Logger.info("Successfully updated PR #{repo}##{number} status to #{status}")
        :ok

      {:error, changeset} ->
        Logger.error("Failed to update PR #{repo}##{number}: #{inspect(changeset.errors)}")
        {:error, "Failed to update pull request status"}
    end
  end

  # Refreshes repository files when a pull request is merged to the default branch.
  # This ensures our internal repository file data stays synchronized with GitHub.
  defp refresh_repository_files_for_merged_pr(%PullRequest{
         repository_id: repository_id,
         repository_full_name: repo_name,
         number: pr_number
       }) do
    Task.start_link(fn ->
      try do
        repository = Repositories.get_repository!(repository_id)

        Logger.info("Refreshing repository files for #{repo_name} after PR ##{pr_number} merge",
          repository_id: repository_id,
          pr_number: pr_number
        )

        # Get a user from the organization to use for GitHub API calls
        user = get_organization_user(repository.organization_id)

        case RepositoryFiles.sync_repository_files(repository, user) do
          {:ok, files} ->
            Logger.info(
              "Successfully refreshed #{length(files)} files for #{repo_name} after PR merge",
              repository_id: repository_id,
              pr_number: pr_number,
              files_count: length(files)
            )

            # Log the repository refresh event
            Events.log_repobot_event(
              "repository_refresh",
              %{
                repository_id: repository_id,
                files_count: length(files),
                trigger: "pr_merge_webhook",
                pr_number: pr_number
              },
              repository.organization_id,
              user && user.id,
              repository_id,
              "success"
            )

          {:error, reason} ->
            Logger.error("Failed to refresh repository files for #{repo_name} after PR merge",
              repository_id: repository_id,
              pr_number: pr_number,
              error: inspect(reason)
            )

            # Log the failure event
            Events.log_repobot_event(
              "repository_refresh",
              %{
                repository_id: repository_id,
                error: inspect(reason),
                trigger: "pr_merge_webhook",
                pr_number: pr_number
              },
              repository.organization_id,
              user && user.id,
              repository_id,
              "failed"
            )
        end
      rescue
        error ->
          Logger.error("Exception during repository file refresh after PR merge",
            repository_id: repository_id,
            pr_number: pr_number,
            error: inspect(error),
            stacktrace: Exception.format_stacktrace(__STACKTRACE__)
          )
      end
    end)

    :ok
  end

  # Helper function to get a user from the organization for GitHub API calls
  defp get_organization_user(organization_id) do
    import Ecto.Query

    from(u in Repobot.Accounts.User,
      join: uo in Repobot.Accounts.UserOrganization,
      on: u.id == uo.user_id,
      where: uo.organization_id == ^organization_id,
      limit: 1
    )
    |> Repobot.Repo.one()
  end
end
